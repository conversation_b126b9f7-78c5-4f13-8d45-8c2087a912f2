import {
  GetSecretValueCommand,
  GetSecretValueResponse,
  SecretsManagerClient,
} from '@aws-sdk/client-secrets-manager';

export class SecretManager {
  private client: SecretsManagerClient = new SecretsManagerClient({
    region: process.env.AWS_REGION,
  });

  async getSecretValue(secretId: string): Promise<GetSecretValueResponse> {
    const input = {
      SecretId: secretId,
    };
    const command = new GetSecretValueCommand(input);
    return this.client.send(command);
  }
}
