import {
  Stack as BaseStack,
  Duration,
  StackProps,
  region_info as RegionInfoLib,
} from 'aws-cdk-lib';
import { Runtime, Tracing } from "aws-cdk-lib/aws-lambda";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import { Config } from "../config/Config";
import * as sns from "aws-cdk-lib/aws-sns";
import { Helper } from "../config/Helper";
import { NameSpacedStage } from "@carrier/backend-lib-infrastructure";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { LambdaFunction } from "aws-cdk-lib/aws-events-targets";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import { resolve } from "path";
import { IVpc } from "aws-cdk-lib/aws-ec2/lib";
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { OtelHelper } from '../otel/OtelHelper';
import {  NameSpacedConstruct } from '@carrier/backend-lib-infrastructure';

interface CommunicationAalrmProps extends StackProps{
  vpc: IVpc;
}

export class CommunicationAalrm extends BaseStack{

    private topic: sns.ITopic;

    private consumerLambda : NodejsFunction;

    private readonly vpc: IVpc;

    private queue: sqs.Queue;

    public constructor( private stage: NameSpacedStage,
        private config: Config,
        id: string,
        props: CommunicationAalrmProps){
        super(stage, id, props);
        const { vpc } = props;
        this.vpc = vpc;
        this.topic =  this.getSNSTopic();
        this.consumerLambda = this.createCommunicationAlarmHandler();

      const otelLogger = Config.getOtelLoggerConfig(this.stage.stageName);

      OtelHelper.initializeOtelLogs(this, this.consumerLambda, otelLogger);

        this.queue = this.createSQSQueue();
        this.createSubscription();
    }


  private buildId(...parts: string[]): string {
    const prefix = Helper.buildId(
      this.config.STAGE as string,
      this.config.COMMUNICATION_ALARM_CONSUMER_LAMBDA_NAME
    );
    return Helper.buildId(prefix, ...parts);
  }

    // eslint-disable-next-line class-methods-use-this
    private getAwsPartitionByRegion(stage = "") {
      const { RegionInfo } = RegionInfoLib;
      const { partition } = RegionInfo.get(stage);
      return partition;
    }

  private createSQSQueue() {
    const id = this.buildId("Queue");
    return new sqs.Queue(this, id, {
      queueName: Helper.convertIdToName(id),
      retentionPeriod: Duration.days(2),
      visibilityTimeout: Duration.seconds(30),
    });
  }


  private createSubscription() {
    const sqsSubscription = new subscriptions.SqsSubscription(this.queue,
      {
        filterPolicyWithMessageBody: {
          clientId: sns.FilterOrPolicy.filter(
            sns.SubscriptionFilter.stringFilter({
              matchPrefixes: [this.config.CJC_FILTER_PREFIX]
            })),
        },
      }
    );
    this.topic.addSubscription(sqsSubscription);
    this.consumerLambda.addEventSource(
      new SqsEventSource(this.queue, {
        batchSize: 100,
        maxBatchingWindow: Duration.seconds(30),
      })
    );
  }

    private getSNSTopic() : sns.ITopic  {
        const partition = this.getAwsPartitionByRegion(this.stage.region);
        const snsData =  {
            topicName: `Lifecycle_Whole-${this.config.STAGE}`,
            arn: `arn:${partition}:sns:${
                this.stage.region
              }:${Config.getCarrierIoAccount(this.config.STAGE)}:Lifecycle_Whole-${
                this.config.STAGE
              }`,           
        }
        return sns.Topic.fromTopicArn(this, snsData.topicName, snsData.arn);
   }
  

  private createCommunicationAlarmHandler() : NodejsFunction {

      const id = this.buildId("lambda");
      const memorySize = this.config.IS_LOCAL
        ? undefined
        : this.config.LAMBDA_MEMORY_SIZE;
      const assumeRoleArn = `arn:${
        this.partition
      }:iam::${Config.getCarrierIoAccount(
        this.config.STAGE
      )}:role/ZephyrPlatformRole-${this.stage.stageName}`;

      const lambda = new NodejsFunction(this, id, {
        environment: {
          STAGE: this.config.STAGE,
          ASSUME_ROLE_ARN: assumeRoleArn,
          LOG_LEVEL: 'INFO',
          BRICKCLASSES_LIST: this.config.BRICKCLASSES_LIST,
          TARGET_STREAM_NAME_DATA: `carrier-io-main-data-pipeline-${this.stage.stageName}`,
          SECRET_DB_ARN: this.config.SECRET_DB_ARN,
          SECRET_DB_KEY: this.config.SECRET_DB_KEY,
          DB_URL: this.config.DB_URL,
          DB_NAME: this.config.DB_NAME,
        },
        runtime: Runtime.NODEJS_20_X,
        memorySize: memorySize,
        timeout: Duration.seconds(30),
        entry: resolve(
          __dirname,
          '../../src/lambdas/CommunicationAlarmConsumerLambda.ts',
        ),
        handler: 'communicationAlarmConsumerLambda',
        tracing: Tracing.ACTIVE,
        functionName: Helper.convertIdToName(id),
        vpc: this.vpc,
      });

      lambda.addToRolePolicy(
            new PolicyStatement({
              effect: Effect.ALLOW,
              actions: ["sts:AssumeRole"],
              resources: [assumeRoleArn],
            })
          );

      lambda.addToRolePolicy(
        new PolicyStatement({
          actions: ['secretsmanager:GetSecretValue'],
          effect: Effect.ALLOW,
          resources: ['*'],
        }),
      );    

      return lambda;
  }
      
}