import { resolve } from 'path';
import { readFileSync } from 'fs';
import { camelCase, upperFirst } from 'lodash';
import { parse as parseDotenv } from 'dotenv';

/**
 * Contains helper methods to build an infrastructure.
 */
export class Helper {
  /**
   * Separator chat between parts of a AWS Construct ID.
   */
  public static ID_SEPARATOR = '-';

  /**
   * Separator chat between parts of a AWS Construct name.
   */
  public static NAME_SEPARATOR = '_';

  /**
   * Separator char between parts of domain name.
   */
  public static DOMAIN_SEPARATOR = '.';

  /**
   * Joins the specified parts of AWS Construct ID into a single string.
   * @param parts Parts of ID.
   */
  public static buildId(...parts: string[]): string {
    return parts.join(this.ID_SEPARATOR);
  }

  /**
   * Converts the specified AWS Construct ID to the construct name.
   * @param id AWS Construct ID.
   */
  public static convertIdToName(id: string): string {
    return id.replace(new RegExp(this.ID_SEPARATOR, 'g'), this.NAME_SEPARATOR);
  }

  /**
   * Builds an application name from the specified parts.
   * @param parts Parts of an application name.
   * @returns An application name.
   */
  public static buildApplication(...parts: string[]): string {
    const text = parts.filter(Boolean).join('-');
    const camelCaseText = camelCase(text);
    return upperFirst(camelCaseText);
  }

  /**
   * Reads environment variables from `../.env` and `../.env.local`, and writes them into the `process.env`.
   */
  public static setupProcessEnv(_stageFromContext: string): void {
    const envFiles: string[] = [
      // resolve(__dirname, `../.env.${stageFromContext ?? 'dev'}`),
      resolve(__dirname, `../../.env`),
      resolve(__dirname, `../../.env.common`),
    ];

    const protectedKeys: Record<string, boolean> = {};
    const knownKeys: Record<string, boolean> = {};

    const { length: filesCount } = envFiles;

    for (let i = 0; i < filesCount; i += 1) {
      const file = envFiles[i];

      let content: string;

      try {
        content = readFileSync(file, 'utf-8');
      } catch (error) {
        console.log('=============error in reading env file===============');
        console.log(error);
        // eslint-disable-next-line no-continue
        continue;
      }

      const rawEnv = parseDotenv(content);

      const keys = Object.keys(rawEnv);
      const { length: keysCount } = keys;

      for (let j = 0; j < keysCount; j += 1) {
        const key = keys[j];

        const isUnknown = !knownKeys[key];
        knownKeys[key] = true;

        if (isUnknown) {
          protectedKeys[key] = typeof process.env[key] !== 'undefined';
        }

        const isProtected = protectedKeys[key];

        if (isProtected) {
          // eslint-disable-next-line no-continue
          continue;
        }

        const value = rawEnv[key];

        if (value) {
          process.env[key] = value;
        }
      }
    }
  }
}
