import { logger } from "../helpers/logger";
import { SQSEvent, SQSRecord } from "aws-lambda";
import {
  DataSetValueType,
  Metric,
  MetricBasicValue,
  Sparkplug,
  TemplateValueType,
} from "../repositories/models/Sparkpug";
import { AlarmGeneratorService } from "../services/AlarmGeneratorService";
import { getBrickClassesWithThresholdValues } from "../helpers/getBrickClassesWithThresholdValues";
import { AlarmPayload } from "models/AlarmPayload";
import { lookupForLeakStatusAndBrickClass } from "../helpers/leakStatusLookup";

const mapEventToSparkplugMessageOrSkip = (
  record: SQSRecord
): Sparkplug | null => {
  try {
    return JSON.parse(JSON.parse(record.body)?.Message);
  } catch (err) {
    logger.error("Event is invalid");
    return null;
  }
};
export const parseSQSEvent = (event: SQSEvent): Sparkplug[] => {
  try {
    return event.Records.map(mapEventToSparkplugMessageOrSkip).filter(
      (r) => r !== null
    ) as Sparkplug[];
  } catch (error) {
    return [];
  }
};

export const virtualAlarmConsumerLambda = async (event: SQSEvent) => {

  const alarmService = new AlarmGeneratorService();
  const records =  parseSQSEvent(event); //

  logger.info(`Parsed SQS Event Records`, {
    records: JSON.stringify(records, null, 2),
  });

  const thresholdBoundaries = getBrickClassesWithThresholdValues();

  logger.info("Threshold boubdaries", JSON.stringify(thresholdBoundaries));

  const payloadList = thresholdBoundaries.map((thresholdObj: any) => {
    let matchingMetric: Metric | null = null;
    let timestamp: number | null = null;
    records.forEach((record) => {
      thresholdObj["assetId"] = record.assetId,
      thresholdObj["assetCategory"] = record.assetCategory,
      thresholdObj["assetSubCategory"] = record.assetSubCategory
      thresholdObj["edgeId"] = record.edgeId,
      thresholdObj["topic"] = record.topic
      thresholdObj["cioTags"] = record.payload?.metrics?.[0].properties?.cioTags;
      thresholdObj["equipmentFamily"] = record.payload?.metrics?.find(m=> m.name  === "equipmentFamily")?.properties?.brickClass.value,
      record.payload?.metrics?.forEach((metric) => {
        if (metric.properties?.brickClass.value === thresholdObj.brickClass) {
          matchingMetric = {
            ...metric,
          } as Metric;
          timestamp =
            (record.payload?.timestamp as number) || record.arrivalTime || null;
        }
      });
      if (matchingMetric && !timestamp) {
        timestamp =
          (record.payload?.timestamp as number) || record.arrivalTime || null;
      }
    });
    if (matchingMetric) {
      return {
        ...thresholdObj,
        measureValue: (matchingMetric as Metric).value,
        arrivalTime: timestamp,
        
      };
    }}).filter((Item : AlarmPayload) => Item);
    
logger.info("List of metrics to generate the alarm", JSON.stringify(payloadList));
const payloadWithoutJudgementFlag =  lookupForLeakStatusAndBrickClass(payloadList);
logger.info("Final metric payload without refleak status brick class", JSON.stringify(payloadWithoutJudgementFlag));

  if (payloadWithoutJudgementFlag.length > 0) {
    logger.debug('Payload length', JSON.stringify(payloadWithoutJudgementFlag.length));
    const publishResults = await alarmService.publishAlarm(payloadWithoutJudgementFlag as any);
    logger.info(`Publish results :: `, {
      publishResults: JSON.stringify(
        publishResults.map((item) => ({
          ...item,
          error: JSON.stringify(
            {
              name: item.error?.name,
              message: item.error?.message,
              stack: item.error?.stack
            },
            null,
            2,
          ),
        })),
        null,
        2,
      ),
    });
  }
  logger.debug('No Alarm or Alerts to Publish');
};


