---
version: 2.1
setup: true

parameters:
  input:
    type: string
    description: >
      A JSON string containing the input parameters.
    default: '{}'

orbs:
  dynamicconfig: cardig/dynamicconfig@1

workflows:
  setup:
    jobs:
      - dynamicconfig/fetch-generate-continue:
          name: Generate Pipeline Configuration
          context: CIRCLECI_USER
          input: << pipeline.parameters.input >>
          filters:
            branches:
              only: /.*$/
            tags:
              only: /.*$/

# vim:expandtab:ft=yaml:sw=2:ts=2