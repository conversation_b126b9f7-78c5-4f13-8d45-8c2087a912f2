import { Stack as BaseStack, StackProps, Stage } from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { Scope } from "aws-cdk-lib/aws-ecs";
import { Construct } from "constructs";
import {Config} from '../config/Config'

export class VpcStack extends BaseStack{
  
   public vpc : ec2.IVpc;

   public constructor(scope: Stage,config  : Config, id : string,props? : StackProps){

    super(scope,id,props);
       
    this.vpc = ec2.Vpc.fromLookup(this,`${id}-vpc`,{
        vpcId: config.VPC_ID,
    })
   }     
}