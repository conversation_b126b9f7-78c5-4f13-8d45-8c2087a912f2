import { literal, union, z } from 'zod';
import Long from 'long';

export const enum MessageType {
  NBIRTH = 'NBIRTH',
  NDEATH = 'NDEATH',
  DBIRTH = 'DBIRTH',
  DDEATH = 'DDEATH',
  NDATA = 'NDATA',
  DDATA = 'DDATA',
  NCMD = 'NCMD',
  DCMD = 'DCMD',
  STATE = 'STATE',
}

export const enum SparkplugTypes {
  Int8 = 'Int8',
  Int16 = 'Int16',
  Int32 = 'Int32',
  UInt8 = 'UInt8',
  UInt16 = 'UInt16',
  UInt32 = 'UInt32',
  Float = 'Float',
  Double = 'Double',
  Int64 = 'Int64',
  UInt64 = 'UInt64',
  Boolean = 'Boolean',
  DateTime = 'DateTime',
  String = 'String',
  Text = 'Text',
  UUID = 'UUID',
  Bytes = 'Bytes',
  File = 'File',
  Template = 'Template',
  DataSet = 'DataSet',
}

export type MetricBasicValue = number | string | boolean | MetricBigIntType;

export const ValueTypeSchema = union([
  literal(SparkplugTypes.UInt32),
  literal(SparkplugTypes.UInt64),
  literal(SparkplugTypes.Double),
  literal(SparkplugTypes.Float),
  literal(SparkplugTypes.Boolean),
  literal(SparkplugTypes.String),
]);

export type ValuesType = z.infer<typeof ValueTypeSchema>;

export interface Sparkplug {
  topic?: string;
  arrivalTime?: number;
  payload?: Payload;
  assetId?: string;
  edgeId?: string;
  entityId?: string;
  assetCategory? : string,
  assetSubCategory ? :string
}

export interface SparkplugWithMessageId extends Sparkplug {
  messageId: string;
}

export interface Payload {
  timestamp?: number | Long;
  seq?: number | Long;
  uuid?: string;
  metrics?: Metric[];
}

export type Metric = MetricBasic | MetricTemplate | MetricDataSet;

interface IMetric<Type extends SparkplugTypes, Value = MetricBasicValue> {
  type: Type;
  value: Value;
  name: string;
  timestamp?: number | Long;
  properties?: MetricProperty;
}

export type MetricBasic = IMetric<BasicTypes>;

export type BasicTypes = Exclude<SparkplugTypes, SparkplugTypes.DataSet | SparkplugTypes.Template>;

export type MetricTemplate = IMetric<SparkplugTypes.Template, TemplateValueType>;

export type TemplateValueType = {
  metrics: Metric[];
  version?: string;
  templateRef?: string;
  parameters?: Array<{
    name: string;
    type: ValuesType;
    value: MetricBasicValue;
  }>;
};

export type MetricDataSet = IMetric<SparkplugTypes.DataSet, DataSetValueType>;
export type DataSetValueType = {
  numOfColumns: MetricBigIntType;
  types: ValuesType[];
  columns: string[];
  rows: Array<MetricBasicValue[]>;
};

export interface MetricProperty {
  brickClass: MetricPropertyValue;
  cioTags: MetricPropertyValue;
  [index: string]: MetricPropertyValue;
}

export interface MetricPropertyValue {
  value?: string;
  type?: string;
}

export type MetricBigIntType = {
  high: number;
  low: number;
  unsigned: boolean;
};
