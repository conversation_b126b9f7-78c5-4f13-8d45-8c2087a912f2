import { connect, connection, ConnectOptions, Mongoose, set } from 'mongoose';
import { SecretManager } from '../services/SecretManagerService';
import { STAGE } from '../enums/stage.enum';
import { logger } from '../helpers';

export interface IDbAdapter {
  connect(): Promise<Mongoose | undefined>;
  closeConnection(): Promise<void>;
}

const secretManager = new SecretManager();

class DatabaseError extends Error {
  constructor(message: string) {
    super();
    this.name = 'DatabaseError';
    this.message = message;
  }
}

export class MongoDBAdapter implements IDbAdapter {
  private dbConnection: Mongoose | undefined;

  private isConnected(): Mongoose | undefined {
    return this.dbConnection;
  }

  public async connect(): Promise<Mongoose | undefined> {
    try {
      if (this.isConnected()) {
        return this.dbConnection;
      }
      let credentials;
      if (process.env.STAGE === STAGE.LOCAL) {
        credentials = {
          username: process.env.DB_USER,
          password: process.env.DB_PASSWORD,
        };
      } else {
        const secretResponse = await secretManager.getSecretValue(
          process.env.SECRET_DB_KEY || '',
        );
        credentials = JSON.parse(secretResponse.SecretString || '');
      }

      const dbUrl = await MongoDBAdapter.getConnectionURL(
        credentials?.username,
        credentials?.password,
      );

      set('strictQuery', false);
      set('debug', (collectionName, method, query, doc) => {
        console.log(`${collectionName}.${method}`, JSON.stringify(query), doc);
      });
      this.dbConnection = await connect(dbUrl, {
        authMechanism: 'SCRAM-SHA-1',
        dbName: process.env.DB_NAME,
      } as ConnectOptions);
    } catch (error) {
      logger.error(`DB connection error ${error}`);
      throw new DatabaseError(
        error instanceof Error
          ? `DB Connection error: ${error.message}`
          : 'Untraceable connection error',
      );
    }

    return this.dbConnection;
  }

  private static async getConnectionURL(
    username: string,
    password: string,
  ): Promise<string> {
    return (process.env.DB_URL ?? '')
      .replace('<username>', username)
      .replace('<password>', password);
  }

  async closeConnection(): Promise<void> {
    await connection?.close();
  }
}
