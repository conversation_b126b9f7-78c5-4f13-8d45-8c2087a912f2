import {
  Stack as BaseStack,
  StackProps,
  region_info as RegionInfoLib,
  Duration,
} from "aws-cdk-lib";
import { IVpc } from "aws-cdk-lib/aws-ec2";
import { Config } from "../config/Config";
import { Helper } from "../config/Helper";
import { NameSpacedStage } from "@carrier/backend-lib-infrastructure";
import { NodejsFunction } from "aws-cdk-lib/aws-lambda-nodejs";
import * as sns from "aws-cdk-lib/aws-sns";
import * as sqs from "aws-cdk-lib/aws-sqs";
import { Runtime, Tracing } from "aws-cdk-lib/aws-lambda";
import * as subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import { SqsEventSource } from "aws-cdk-lib/aws-lambda-event-sources";
import { resolve } from "path";
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { getBrickClassesWithThresholdValues } from "../../src/helpers/getBrickClassesWithThresholdValues";
import { OtelHelper } from '../otel/OtelHelper';

interface LambdaStackProps extends StackProps {
  vpc: IVpc;
}

export class VirtualAlarmConsumerStack extends BaseStack {
  /**
   * The VPC to which the stack is deployed.
   */
  private readonly vpc: IVpc;

  private consumerLambda: NodejsFunction;

  private topic: sns.ITopic;

  private queue: sqs.Queue;

  private subscriptions: sns.Subscription;

  private buildId(...parts: string[]): string {
    const prefix = Helper.buildId(
      this.config.STAGE as string,
      this.config.VIRTUAL_ALARM_CONSUMER_LAMBDA_NAME
    );
    return Helper.buildId(prefix, ...parts);
  }

  // eslint-disable-next-line class-methods-use-this
  private getAwsPartitionByRegion(stage = "") {
    const { RegionInfo } = RegionInfoLib;
    const { partition } = RegionInfo.get(stage);
    return partition;
  }

  private getSNSTopicFromArn(): sns.ITopic {
    const partition = this.getAwsPartitionByRegion(this.stage.region);
    const snsData = {
      topicName: `Data_Whole-${this.config.STAGE}`,
      arn: `arn:${partition}:sns:${
        this.stage.region
      }:${Config.getCarrierIoAccount(this.config.STAGE)}:Data_Whole-${
        this.config.STAGE
      }`,
    };
    return sns.Topic.fromTopicArn(this, snsData.topicName, snsData.arn);
  }

  private createSQSQueue() {
    const id = this.buildId("Queue");
    return new sqs.Queue(this, id, {
      queueName: Helper.convertIdToName(id),
      retentionPeriod: Duration.days(2),
      visibilityTimeout: Duration.seconds(30),
    });
  }
  private createSubscription() {
    const brickClassList = this.config.BRICKCLASSES_LIST ? JSON.parse(this.config.BRICKCLASSES_LIST) : null;
    const allowedBrickClassList = getBrickClassesWithThresholdValues(brickClassList)

    const sqsSubscription = new subscriptions.SqsSubscription(this.queue, {
      filterPolicyWithMessageBody: {
        "payload.metrics.properties.brickClass": sns.FilterOrPolicy.policy({
          value: sns.FilterOrPolicy.filter(
            sns.SubscriptionFilter.stringFilter({
              allowlist : allowedBrickClassList.map(bricClassObj => bricClassObj.brickClass) as string[]
            })
          ),
        }),
      },
    });
    this.topic.addSubscription(sqsSubscription);
    this.consumerLambda.addEventSource(
      new SqsEventSource(this.queue, {
        batchSize: 100,
        maxBatchingWindow: Duration.seconds(30),
      })
    );
  }
  public constructor(
    private stage: NameSpacedStage,
    private config: Config,
    id: string,
    props: LambdaStackProps
  ) {
    super(stage, id, props);
    this.config = config;
    const { vpc } = props;
    this.vpc = vpc;
    this.consumerLambda = this.createConsumerLambdaFunction();
    this.topic = this.getSNSTopicFromArn();
    this.queue = this.createSQSQueue();

    const otelLogger = Config.getOtelLoggerConfig(this.stage.stageName);

    OtelHelper.initializeOtelLogs(this, this.consumerLambda, otelLogger);

    this.createSubscription();
  }

  private createConsumerLambdaFunction(): NodejsFunction {
    const id = this.buildId("lambda");
    const memorySize = this.config.IS_LOCAL
      ? undefined
      : this.config.LAMBDA_MEMORY_SIZE;
    const assumeRoleArn = `arn:${
      this.partition
    }:iam::${Config.getCarrierIoAccount(
      this.config.STAGE
    )}:role/ZephyrPlatformRole-${this.stage.stageName}`;
    const lambda = new NodejsFunction(this, id, {
      runtime: Runtime.NODEJS_20_X,
      environment: {
        ASSUME_ROLE_ARN: assumeRoleArn,
        LOG_LEVEL: "INFO",
        BRICKCLASSES_LIST : this.config.BRICKCLASSES_LIST,
        TARGET_STREAM_NAME_DATA: `carrier-io-main-data-pipeline-${this.stage.stageName}`
      },
      memorySize,
      timeout: Duration.seconds(30),
      entry: resolve(
        __dirname,
        "../../src/lambdas/VirtualAlarmConsumerLambda.ts"
      ),
      handler: "virtualAlarmConsumerLambda",
      tracing: Tracing.ACTIVE,
      functionName: Helper.convertIdToName(id),
      vpc: this.vpc,
    });

    lambda.addToRolePolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: ["sts:AssumeRole"],
        resources: [assumeRoleArn],
      })
    );
    return lambda;
  }
}
