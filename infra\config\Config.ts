import { hostname } from 'os';
import { STAGE_NAME } from './constants';
import { OtelConfig } from '../otel/Otel.Config.type';

export class Config {

    public readonly VPC_ID = process.env.VPC_ID;

    public readonly STAGE =  process.env.STAGE || "";

    public readonly IS_LOCAL = !process.env.STAGE;

    public readonly AWS_ACCOUNT = process.env.CDK_DEFAULT_ACCOUNT || '';

    public readonly AWS_REGION = process.env.CDK_DEFAULT_REGION || '';

    public readonly VIRTUAL_ALARM_CONSUMER_LAMBDA_NAME = process.env.VIRTUAL_ALARM_CONSUMER_LAMBDA_NAME || '';


    public readonly COMMUNICATION_ALARM_CONSUMER_LAMBDA_NAME = process.env.COMMUNICATION_ALARM_CONSUMER_LAMBDA_NAME || '';
    
    public readonly LAMBDA_MEMORY_SIZE = 512;

    public readonly BRICKCLASSES_LIST = JSON.stringify(process.env.BRICKCLASSES_LIST || [
      {
        brickClass: "Cool_Valve_Position_Deviation_Status",
        alarmThreshold: 25,
        alertThreshold: 15
      },
      {
        brickClass: "Heat_Valve_Position_Deviation_Status",
        alarmThreshold: 35,
        alertThreshold: 30
      },
      {
        brickClass : "Refrigerant_Leak_Status",
        alarmThreshold: 1,
        alertThreshold: 0,
      }
    ])

    public readonly MODEL_LIST = JSON.stringify(process.env.MODEL_LIST || []);

    public readonly CJC_FILTER_PREFIX = "cjc-";

    public readonly SECRET_DB_ARN = process.env.SECRET_DB_ARN || '';

    public readonly SECRET_DB_KEY = process.env.SECRET_DB_KEY || '';

    public readonly DB_URL = process.env.DB_URL || '';

    public readonly DB_NAME = process.env.DB_NAME || '';
    

    public constructor(stageFromContext :STAGE_NAME ){
        this.STAGE = stageFromContext ?? (process.env.STAGE || hostname()).toLowerCase().replace(/[\W_]/g, '');
    }

    static getCarrierIoAccount(env: string) {
        switch (env) {
          case 'dev':
            return '************';
          case 'qa':
            return '************';
          case 'preprod':
            return '************';
          case 'prod':
            return '************';
          case 'dev-cn':
            return '************';
          case 'qa-cn':
            return '************';
          case 'preprod-cn':
            return '************';
          case 'prod-cn':
            return '************';
          default:
            return '************';
        }
      }

  static getOtelLoggerConfig(appStageName: string) {
    const defaultConfig: OtelConfig = {
      OTEL_INITILIZER_LAMBDA_LAYER_ARN: 'arn:aws:lambda:us-east-1:************:layer:otel-initializer:10',
      OTEL_BACKEND_EXPORTERS: '["newrelic"]',
      OTEL_LOGS_SAMPLING_RATE: '80',
    };
    const otelLoggerConfig: Record<string, OtelConfig> = {
      dev: {
        ...defaultConfig,
      },
      qa: {
        ...defaultConfig,
      },
      preprod: {
        ...defaultConfig,
      },
      prod: {
        ...defaultConfig,
        DISABLE_LOG_SAMPLING: 'true',
        OTEL_LOGS_SAMPLING_RATE: '0',
      },
    };
    return otelLoggerConfig[appStageName as keyof typeof otelLoggerConfig];
  }

};

// vim:expandtab:ft=typescript:sw=4:ts=4
