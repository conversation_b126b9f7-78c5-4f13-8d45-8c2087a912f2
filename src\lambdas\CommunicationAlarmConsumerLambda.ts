import { logger } from "../helpers/logger";
import { SQSEvent, SQSRecord } from "aws-lambda";
import {Sparkplug} from "../repositories/models/Sparkpug";
import { AlarmGeneratorService } from "../services/AlarmGeneratorService";
import { NodeAlarmPayload } from "../models/AlarmPayload";
import { MessageType } from '../repositories/models/Sparkpug';
import {edgeAlarmCode} from "../enums/alarmcode.enum"

const mapEventToSparkplugMessageOrSkip = (
  record: SQSRecord
): Sparkplug | null => {
  try {
    return JSON.parse(JSON.parse(record.body)?.Message);
  } catch (err) {
    logger.error("Event is invalid");
    return null;
  }
};
export const parseSQSEvent = (event: SQSEvent): Sparkplug[] => {
  try {
    return event.Records.map(mapEventToSparkplugMessageOrSkip).filter(
      (r) => r !== null
    ) as Sparkplug[];
  } catch (error) {
    return [];
  }
};

export const communicationAlarmConsumerLambda = async (event: SQSEvent) => {
  const alarmService = new AlarmGeneratorService();
  const records = parseSQSEvent(event);

  logger.info(`Parsed SQS Event Records`, {
    records: JSON.stringify(records, null, 2),
  });

  let PayloadList: NodeAlarmPayload[] = [];
  let ReceivedMessageType: MessageType;
  records.forEach((record: any) => {
    ReceivedMessageType = record?.topic?.split("/")[2]
    let payload: NodeAlarmPayload = {
      payloadTopicMsg: ReceivedMessageType || undefined,
      topic: record?.topic,
      arrivalTime: record?.arrivalTime,
      alarmCode: edgeAlarmCode.TG01,
      alarmThreshold: 1,
      clientId: record?.clientId
    }
    PayloadList.push(payload);
  });

  PayloadList = PayloadList.filter((Item: NodeAlarmPayload) => [MessageType.NBIRTH, MessageType.NDEATH].includes(Item.payloadTopicMsg as MessageType))

  if (PayloadList.length > 0) {
    logger.debug('Payload length', JSON.stringify(PayloadList.length));
    const publishResults = await alarmService.publishNodeMessagesAlarm(PayloadList as any);
    logger.info(`Publish results :: `, {
      publishResults: JSON.stringify(
        publishResults.map((item) => ({
          ...item,
          error: JSON.stringify(
            {
              name: item.error?.name,
              message: item.error?.message,
              stack: item.error?.stack
            },
            null,
            2,
          ),
        })),
        null,
        2,
      ),
    });
  }
  logger.debug('No Alarm to Publish');
}