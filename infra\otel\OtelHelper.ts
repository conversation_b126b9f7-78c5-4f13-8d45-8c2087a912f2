import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { LayerVersion, ILayerVersion } from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import { Effect, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { Stack } from 'aws-cdk-lib';
import { OtelConfig } from './Otel.Config.type';

export class OtelHelper {
  private static _otelLayer: ILayerVersion;

  public static initializeOtelLogs(scope: Construct, lambdaFunction: NodejsFunction, otelConfig: OtelConfig) {
    this._addOtelLayer(scope, lambdaFunction, otelConfig.OTEL_INITILIZER_LAMBDA_LAYER_ARN);
    this._setLambdaPermisson(lambdaFunction);
    this._setLambdaEnvForOtelLogs(lambdaFunction, otelConfig);
  }

  private static _addOtelLayer(scope: Construct, lambdaFunction: NodejsFunction, layerArn: string) {
    const otelLayer = this._getOtelLayer(scope, layerArn);
    lambdaFunction.addLayers(otelLayer);
  }

  private static _getOtelLayer(scope: Construct, layerArn: string) {
    if (this._otelLayer && Stack.of(scope) === Stack.of(this._otelLayer)) {
      return this._otelLayer;
    }
    this._otelLayer = LayerVersion.fromLayerVersionArn(scope, 'otel-initializer', layerArn);
    return this._otelLayer;
  }

  private static _setLambdaPermisson(lambdaFunction: NodejsFunction) {
    lambdaFunction.addToRolePolicy(
      new PolicyStatement({
        actions: ['ssm:GetParameter*'],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );
  }

  private static _setLambdaEnvForOtelLogs(lambdaFunction: NodejsFunction, otelConfig: OtelConfig) {
    const otelConfigData = this._getDefaultConfig(lambdaFunction, otelConfig);
    for (const [key, value] of Object.entries(otelConfigData)) {
      lambdaFunction.addEnvironment(key, value as string);
    }
  }

  private static _getDefaultConfig(lambdaFunction: NodejsFunction, otelConfig: OtelConfig) {
    const otelDefaultConfig = {
      OTEL_BACKEND_EXPORTERS: '["newrelic"]',
      OTEL_LOG_LEVEL: '["info", "error", "warn"]',
      OTEL_SERVICE_NAME: lambdaFunction.node.id,
      AWS_LAMBDA_EXEC_WRAPPER: '/opt/otel-initializer',
    };
    return {
      ...otelDefaultConfig,
      ...otelConfig,
    };
  }
}
