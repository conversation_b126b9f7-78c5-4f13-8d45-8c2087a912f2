<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-pipeline-virtual-alarm-consumer\src\__tests__\unit\services\NDataSNSService.test.ts">
    <testCase name="publish alarms should handle error for ParseSQSEvent" duration="4"/>
    <testCase name="publish alarms should publish alarm without error" duration="78"/>
    <testCase name="publish alarms Reset alarm when we receive NBirth Message" duration="3"/>
    <testCase name="publish alarms Raise alarm when we receive Ndeath Message" duration="3"/>
    <testCase name="publish alarms should throw if pipelineClient.publish fails" duration="11"/>
    <testCase name="publish alarms should throw if generateNodeAlarmMessage fails" duration="3"/>
  </file>
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-pipeline-virtual-alarm-consumer\src\__tests__\unit\services\DDataSNSService.test.ts">
    <testCase name="publish alarms alarm boundaries should have alarm and alert code" duration="57"/>
    <testCase name="publish alarms only filter DDATA records if brick class found" duration="2"/>
    <testCase name="publish alarms should publish alarm without error" duration="13"/>
    <testCase name="publish alarms should handle error" duration="0"/>
    <testCase name="publish alarms it should publish alarm" duration="6"/>
    <testCase name="publish alarms Raise the alarm when refleak status is 1 and measure value is greater than alarm threshold" duration="6"/>
    <testCase name="publish alarms Raise the alert when refleak status is 0 and measure value is greater than alert threshold" duration="3"/>
    <testCase name="publish alarms Reset the alarm and alert measure value is less than alert threshold" duration="4"/>
    <testCase name="publish alarms Reset the alarm when measure value is less than than alarm threshold and greater than alert threshold" duration="3"/>
    <testCase name="publish alarms Reset the alarm when meausre value is less than than alarm threshold and raise the alert greater than alert threshold" duration="1"/>
    <testCase name="publish alarms Reset alarm and alert when measure value is less than alert threshold" duration="1"/>
  </file>
</testExecutions>