
export interface CurrentJob {
  jobId: string | null;
  jobStatus: string | null;
  completedAt: Date | null;
  targets: Targets[];
}

export interface EdgeJobDetails {
  currentJob?: CurrentJob;
}

export interface EdgeSyncConfig {
  batchSize: number;
  maxRetries: number;
  includeAssets: boolean;
  filters: any;
}

export interface Edge {
  edgeNodeId: string;
  siteName: string;
  edgeId: string | null;
  isOnline: boolean | null;
  targets: Targets[];
  currentJob?: CurrentJob;
  createdAt?: Date | null;
  updatedAt?: Date | null;
}

export interface Targets {
  targetName: string;
  version: string;
}

export type IAllEdges = Partial<Edge>;
