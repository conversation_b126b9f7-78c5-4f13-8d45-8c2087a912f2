
import { BrickClassWithAlarmBoundaries } from "../models/AlarmPayload";
import {SubSystems} from "../enums/subsystems.enum"

 export const getBrickClassesWithThresholdValues = (brickClasses? : BrickClassWithAlarmBoundaries[]) =>{
     const brickClassList : BrickClassWithAlarmBoundaries[]= process.env.BRICKCLASSES_LIST ? JSON.parse(process.env.BRICKCLASSES_LIST) : brickClasses;
          return brickClassList.reduce((alarmBoundaries : BrickClassWithAlarmBoundaries[] ,brickClassObj : BrickClassWithAlarmBoundaries) => {
            Object.keys(SubSystems).map((e)=>{
              const alarmBounaryItem = {
                   brickClass : `${e}_${brickClassObj.brickClass}`,
                   alarmCode : `A${e.slice(-1)}68`,
                   alertCode :  `T${e.slice(-1)}68`,
                   alarmThreshold: brickClassObj.alarmThreshold,
                   alertThreshold: brickClassObj.alertThreshold
              }
              alarmBoundaries.push(alarmBounaryItem)
            })
            return alarmBoundaries;
          },[]);
 }