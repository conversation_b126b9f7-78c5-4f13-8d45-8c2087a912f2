
import { PipelinePublisherService, SparkplugMessage } from '@carrier-io/backend-lib-pipeline-sdk';
import { logger } from '../helpers/logger';
import { v4 as uuidv4 } from 'uuid';
import { MessageType, SparkplugTypes } from '../repositories/models/Sparkpug'
import { AlarmPayload,NodeAlarmPayload } from '../models/AlarmPayload';


export class AlarmGeneratorService {
  private pipelineClient = PipelinePublisherService.newWithEnvVar();
  async publishAlarm(payloadList: Array<AlarmPayload>) {
    try {
      const messages = payloadList
        .map((payload) => {
          try {
            return AlarmGeneratorService.generateAlarmMessage(payload);
          } catch (err) {
            logger.error((err as Error).message);
            return null;
          }
        })
        .filter((item): item is Exclude<typeof item, null> => !!item);
      logger.info("Before alarm publish",{ messages: JSON.stringify(messages, null, 2) });
      logger.debug('Before publish Alarm messages', { messages: JSON.stringify(messages, null, 2) });
      return  this.pipelineClient.publish(messages); 
    } catch (err) {
      logger.error(`An error has encountered`, { err });
      throw err;
    }
  }

  public static generateAlarmMessage(payload: AlarmPayload) {
    const topic: SparkplugMessage["topic"] = payload.topic;
    const clientId: SparkplugMessage["clientId"] = "N/A";
    const arrivalTime: SparkplugMessage["arrivalTime"] = payload.arrivalTime;
    const traceId: SparkplugMessage["traceId"] = `${uuidv4()}`;
    const assetCategory: SparkplugMessage["assetCategory"] =
      payload.assetCategory;
    const assetSubCategory: SparkplugMessage["assetSubCategory"] =
      payload.assetSubCategory;
    const uuid = `${uuidv4()}`;


    let isAlarmActive = false;
    let isAlertActive = false;
    
    const measureValue = Number(payload.measureValue);
    const { refleakStatus, alarmThreshold, alertThreshold } = payload;
    
    // Scenario 1: Raise the alarm when refleakStatus = 1 and measureValue ≥ alarmThreshold
    if (refleakStatus && measureValue >= alarmThreshold) {
        isAlarmActive = true;
    }
    
    // Scenario 2: Reset the alarm when measureValue < alarmThreshold
    if (measureValue < alarmThreshold && (measureValue > alertThreshold && !refleakStatus)) {
        isAlarmActive = false;
        isAlertActive = true;
    }
    
    if (measureValue < alarmThreshold && (measureValue > alertThreshold && refleakStatus)) {
      isAlarmActive = false;
    }
    // Scenario 3: Reset both alarm and alert when measureValue < alertThreshold
    if (measureValue < alertThreshold) {
        isAlarmActive = false;
        isAlertActive = false;
    } else {
        // Scenario 4: Raise the alert when refleakStatus = 0 and measureValue > alertThreshold
        isAlertActive = Number(refleakStatus) === 0 && measureValue > alertThreshold;
       
    }
    
    // Prepare Metrics
    let metrics: any[] = [
        {
            name: "picControllerVersion",
            type: "String",
            value: "UC",
            properties: {
                brickClass: { value: "PIC_Controller_Version", type: "String" },
            },
        },
        {
            name: "equipmentFamily",
            type: "String",
            value: payload.equipmentFamily,
            properties: {
                brickClass: { value: "Equipment_Family", type: "String" },
            },
        },
        {
            name: "cioTags",
            type: "String",
            value: payload.cioTags.value,
        },
        {
            name: payload.brickClass,
            type: typeof measureValue === "number" ? SparkplugTypes.Double : SparkplugTypes.String,
            value: measureValue,
            properties: {
                brickClass: { value: payload.brickClass, type: SparkplugTypes.String },
            },
        },
    ];
    
    // Add Alarm or Alert based on conditions
    if (isAlarmActive) {
        metrics.push({
            name: payload.alarmCode,
            type: SparkplugTypes.Boolean,
            value: isAlarmActive,
            properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
        });
    } else if (!isAlarmActive && isAlertActive && !refleakStatus) {
      const alarmAlertMetric = [
        {
            name: payload.alarmCode,
            type: SparkplugTypes.Boolean,
            value: isAlarmActive,
            properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
        },
        {
            name: payload.alertCode,
            type: SparkplugTypes.Boolean,
            value: isAlertActive,
            properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
        },
    ];
    metrics = [...metrics, ...alarmAlertMetric];
    } else if (isAlertActive) {
        metrics.push({
            name: payload.alertCode,
            type: SparkplugTypes.Boolean,
            value: isAlertActive,
            properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
        });
    } else {
        // Reset both Alarm and Alert
        const alarmAlertMetric = [
            {
                name: payload.alarmCode,
                type: SparkplugTypes.Boolean,
                value: isAlarmActive,
                properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
            },
            {
                name: payload.alertCode,
                type: SparkplugTypes.Boolean,
                value: isAlertActive,
                properties: { brickClass: { value: "Alarm", type: SparkplugTypes.String } },
            },
        ];
        metrics = [...metrics, ...alarmAlertMetric];
    }
      const sparkplugMessage: SparkplugMessage = {
        topic,
        payload: {
          timestamp: payload.arrivalTime,
          metrics: metrics,
          uuid,
        },
        clientId,
        arrivalTime,
        traceId,
        assetCategory,
        assetSubCategory,
        assetId: payload.assetId,
      };

      logger.info(`alarm message`, { sparkplugMessage });
      logger.debug(`generateAlarmMessage`, { sparkplugMessage });
      return sparkplugMessage;
  }

  async publishNodeMessagesAlarm(payloadList: Array<NodeAlarmPayload>) {
    try {
      const messages = payloadList
        .map((payload) => {
          try {
            return AlarmGeneratorService.generateNodeAlarmMessage(payload);
          } catch (err) {
            logger.error((err as Error).message);
            return null;
          }
        })
        .filter((item): item is Exclude<typeof item, null> => !!item);
      logger.info("Before alarm publish",{ messages: JSON.stringify(messages, null, 2) });
      logger.debug('Before publish Alarm messages', { messages: JSON.stringify(messages, null, 2) });
      return  this.pipelineClient.publish(messages); 
    } catch (err) {
      logger.error(`An error has encountered`, { err });
      throw err;
    }
  }

  public static generateNodeAlarmMessage(payload: NodeAlarmPayload) {
    const alarmBrickClass = 'Alarm';
    //converting NDEATH or NBIRTH to NDATA for handling Node alarm
    let pattern = new RegExp(`${MessageType.NBIRTH}|${MessageType.NDEATH}`, 'g');
    const topic: SparkplugMessage['topic'] = payload?.topic?.replace(pattern,MessageType.NDATA);
    const clientId: SparkplugMessage['clientId'] = payload.clientId ?? "N/A";
    const arrivalTime: SparkplugMessage['arrivalTime'] = payload.arrivalTime;
    const traceId: SparkplugMessage['traceId'] = `${uuidv4()}`;
    const uuid = `${uuidv4()}`;
    const alarmCode = payload.alarmCode;
    const isActive = payload.payloadTopicMsg == MessageType.NDEATH;
    const assetCategory: SparkplugMessage["assetCategory"] = "CJC_GW";

    let metrics: any = [
      {
        "name": payload.alarmCode,
        "type": SparkplugTypes.Boolean,
        "value": isActive,
        "properties": {
          "brickClass": {
            "value": alarmBrickClass,
            "type": SparkplugTypes.String
          }
        }
      },
      {
        "name": "equipmentFamily",
        "type": "String",
        "value": "CJC_EYE",
        "properties": {
          "brickClass": {
            "value": "Equipment_Family",
            "type": "String"
          }
        }
      },
      {
        "name": "picControllerVersion",
        "type": "String",
        "value": "GW",
        "properties": {
          "brickClass": {
            "value": "PIC_Controller_Version",
            "type": "String"
          }
        }
      }
    ]
    const sparkplugMessage: SparkplugMessage = {
      topic,
      payload: {
        timestamp : payload.arrivalTime,
        metrics: metrics,
        uuid,
      },
      clientId,
      assetCategory,
      arrivalTime,
      traceId
    };
    logger.info(`alarm message`,{sparkplugMessage});
    logger.debug(`generateAlarmMessage`, { sparkplugMessage });
    return sparkplugMessage;
  }
}
