////////////////////////////
// IMPORTS
////////////////////////////
import { Config } from './config/Config';
import {ZephyrApp} from '@zephyr/backend-lib-infrastructure';
import { NameSpacedStage } from '@carrier/backend-lib-infrastructure';
import * as constants  from './config/constants'
import { Helper } from './config/Helper';
import { VpcStack } from './stacks/Vpc.stack';
import { Environment } from 'aws-cdk-lib';
import { VirtualAlarmConsumerStack } from './stacks/VirtualAlarmConsumer.stack';
import {CommunicationAalrm} from "./stacks/CommunicationAlarmConsumer.stack";

export class App extends ZephyrApp {
     public constructor(){
        super({
            appName : constants.appStackName
        })
        const stageFromContext = this.node.tryGetContext('stage');
        Helper.setupProcessEnv(stageFromContext);

        const config = new Config(stageFromContext);
        const stackEnv: Environment = config.IS_LOCAL
        ? {}
        : {
            region: config.AWS_REGION,
            account: config.AWS_ACCOUNT,
          };

          const vpcStack = new VpcStack(this.stage as NameSpacedStage, config, constants.vpcStackName, {
            env: {
              region: config.AWS_REGION,
              account: config.AWS_ACCOUNT,
            },
          });
          
        // For Type B as of now we don't need this so disabling this stack.
        //  new VirtualAlarmConsumerStack(this.stage,config,`${config.VIRTUAL_ALARM_CONSUMER_LAMBDA_NAME}-Stack`,{
        //   vpc : vpcStack.vpc
        //  })

         new CommunicationAalrm(this.stage,config,`${config.COMMUNICATION_ALARM_CONSUMER_LAMBDA_NAME}-Stack`,{
          vpc : vpcStack.vpc,
          env: stackEnv
         })
     }
}
