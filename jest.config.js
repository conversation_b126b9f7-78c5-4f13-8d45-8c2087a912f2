const { compilerOptions } = require('./tsconfig.json');

module.exports = {
  preset: 'ts-jest',
  testMatch: ['**/+(*.)+(spec|test).+(ts|js)?(x)'],
  resolver: 'ts-jest-resolver',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  testEnvironment: 'node',
  moduleFileExtensions: ['ts', 'js'],
  modulePathIgnorePatterns: ['<rootDir>/dist/'],
  transform: {
    '^.+\\.ts?$': 'ts-jest',
  },
  testResultsProcessor: 'jest-sonar-reporter',
  coverageDirectory: './coverage',
  collectCoverage: true,
  coverageReporters: ['lcov', 'text'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/__tests__/**',
    '!src/repositories/**',
    '!src/enums/**',
  ],
  verbose: true,
  testTimeout: 100000,
};

// vim:expandtab:ft=javascript:sw=2:ts=2
