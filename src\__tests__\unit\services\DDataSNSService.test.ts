import { mockClient } from 'aws-sdk-client-mock';
import { parseSQSEvent, virtualAlarmConsumerLambda } from '../../../lambdas/VirtualAlarmConsumerLambda';
import { getBrickClassesWithThresholdValues } from '../../../helpers/getBrickClassesWithThresholdValues';
import  eventData from '../mock/event.json';
import  alarmBoundaries from '../mock/brickClassesWithAlarmBoundaries.json'
import { AlarmGeneratorService } from '../../../services/AlarmGeneratorService';
import { PipelinePublisherService } from '@carrier-io/backend-lib-pipeline-sdk';

beforeAll(() => {
  process.env.BRICKCLASSES_LIST = JSON.stringify([
    {
      brickClass: 'Cool_Valve_Position_Deviation_Status',
      alarmThreshold: 25,
      alertThreshold: 15,
    },
    {
      brickClass: 'Heat_Valve_Position_Deviation_Status',
      alarmThreshold: 35,
      alertThreshold: 30,
    },
    {
      brickClass: 'Refrigerant_Leak_Status',
      alarmThreshold: 1,
      alertThreshold: 1,
    },
  ]);
  process.env.TARGET_STREAM_NAME_DATA = `sample-data-pipeline-dev`;
});

jest.mock('@carrier-io/backend-lib-pipeline-sdk');

const mockPublish = jest.fn();

(PipelinePublisherService.newWithEnvVar as jest.Mock).mockReturnValue({
  publish: mockPublish,
});

describe('publish alarms', () => {
  it('alarm boundaries should have alarm and alert code', async () => {
    const response = getBrickClassesWithThresholdValues();
    console.log('response', JSON.stringify(response));
    expect(response).toEqual(alarmBoundaries);
  });
  it('only filter DDATA records if brick class found', async () => {
    const parsedEvent = parseSQSEvent(eventData as any);
    console.log('JSON.parse(eventData.body)', parseSQSEvent(eventData as any));
    expect(
      parsedEvent[0].payload?.metrics?.filter(
        (e) => e.properties?.brickClass.value === 'Chiller_Unit_Number_Status',
      ),
    ).toHaveLength(1);
  });
  it('should publish alarm without error', async () => {
    await virtualAlarmConsumerLambda(eventData as any);
  });

  it('should handle error', async () => {
    const errorEventData = {
      Records: [
        {
          body: null,
        },
      ],
    };
    const parsedEvent = parseSQSEvent(errorEventData as any);
    expect(parsedEvent).toEqual([]);
  });

  it('it should publish alarm', async () => {
    const payloadList = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 26,
      arrivalTime: 1741110164000,
      refleakStatus: 1,
    };
    let service = new AlarmGeneratorService();

    await service.publishAlarm([payloadList] as any);

    expect(mockPublish).toHaveBeenCalledTimes(1);

    const publishedMessages = mockPublish.mock.calls[0][0];

    expect(publishedMessages).toHaveLength(1);
  });

  it(`Raise the alarm when refleak status is 1 and measure value is greater than alarm threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 26,
      arrivalTime: 1741110164000,
      refleakStatus: 1,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && metric.value,
      ),
    ).toBe(true);
  });
  it(`Raise the alert when refleak status is 0 and measure value is greater than alert threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 16,
      arrivalTime: 1741110164000,
      refleakStatus: 0,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );

    console.log(
      'one metric',
      JSON.stringify(sparkplugMessage.payload.metrics[4]),
    );
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'TA68' && metric.value,
      ),
    ).toBe(true);
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && !metric.value,
      ),
    ).toBe(true);
  });

  it(`Reset the alarm and alert measure value is less than alert threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 14,
      arrivalTime: 1741110164000,
      refleakStatus: 0,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );

    console.log(
      'one metric',
      JSON.stringify(sparkplugMessage.payload.metrics[4]),
    );
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && !metric.value,
      ),
    ).toBe(true);
  });

  it(`Reset the alarm when measure value is less than than alarm threshold and greater than alert threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 24,
      arrivalTime: 1741110164000,
      refleakStatus: 0,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );

    console.log(
      'one metric',
      JSON.stringify(sparkplugMessage.payload.metrics[4]),
    );
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && !metric.value,
      ),
    ).toBe(true);
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'TA68' && metric.value,
      ),
    ).toBe(true);
  });
  it(`Reset the alarm when meausre value is less than than alarm threshold and raise the alert greater than alert threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 24,
      arrivalTime: 1741110164000,
      refleakStatus: 1,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );

    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && !metric.value,
      ),
    ).toBe(true);
  });

  it(`Reset alarm and alert when measure value is less than alert threshold`, () => {
    const payload = {
      brickClass: 'CircuitA_Cool_Valve_Position_Deviation_Status',
      alarmCode: 'AA68',
      alertCode: 'TA68',
      alarmThreshold: 25,
      alertThreshold: 15,
      assetId: '',
      assetCategory: 'EDGE_UCseries',
      assetSubCategory: '',
      edgeId: '',
      topic: 'spBv1.0/ahp/DDATA/893223434/UCUCRUAGP422CKZGA4723W66617',
      cioTags: {
        value: 'zephyr_tag_97817702-6a1c-4fa5-b86a-e829341b8296',
        type: 'String',
      },
      equipmentFamily: 'Equipment Family',
      measureValue: 10,
      arrivalTime: 1741110164000,
      refleakStatus: 1,
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateAlarmMessage(
      payload as any,
    );

    console.log(
      'one metric',
      JSON.stringify(sparkplugMessage.payload.metrics[4]),
    );
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'AA68' && !metric.value,
      ),
    ).toBe(true);
    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'TA68' && !metric.value,
      ),
    ).toBe(true);
  });
});

