import { MessageType } from 'repositories/models/Sparkpug';

export interface updateDetailsData {
  edgeId: string;
  MessageType: MessageType.NBIRTH | MessageType.NDEATH;
  connectivityStatus: boolean;
  targetDetails?: Targets[];
}

export interface Targets {
  targetName: string;
  version: string;
}

export enum TargetFirmwareNames {
  ESP32_firmware = 'ESP32_firmware',
  ESP32_filesystem = 'ESP32_filesystem',
  Claim_cert = 'Claim_cert',
  Claim_private = 'Claim_private',
  LTEmodule = 'LTEmodule',
  AmazonRootCA1 = 'AmazonRootCA1',
  RX651_CHL = 'RX651_CHL',
}