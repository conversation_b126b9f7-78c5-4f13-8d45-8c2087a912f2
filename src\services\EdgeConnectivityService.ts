import { MongoDBAdapter } from '../adapters/mongodb';
import { EdgeModel } from '../schemas/alledges.schemas';
import { logger } from '../helpers/logger';
import { MessageType } from 'repositories/models/Sparkpug';
import { updateDetailsData, Targets } from '../enums/edgeupdate.enum';

export class EdgeConnectivityService {
  private mongoDb: MongoDBAdapter;
  constructor() {
    this.mongoDb = new MongoDBAdapter();
  }

  private async init() {
    await this.mongoDb.connect();
  }

  public async updateEdgeConnectivityStatus(
    edgeId: string,
    updateDetails: updateDetailsData,
  ): Promise<any> {
    try {

      await this.init();

			logger.info(`Updating the Edge Connectivity Status for ${edgeId}`, {
        updateDetails: JSON.stringify(updateDetails, null, 2),
      });

      if (updateDetails.MessageType == MessageType.NBIRTH) {
        
				await this.updateConnectivityAndTargetsToDB(
          edgeId,
          updateDetails.connectivityStatus,
          updateDetails.targetDetails,
        );

      } else if (updateDetails.MessageType == MessageType.NDEATH) {

        await this.updateConnectivityStatusToDB(
          edgeId,
          updateDetails.connectivityStatus,
        );

      }

    } catch (err) {
      logger.error(
        `Error while Updating the Edge Connectivity Status for ${edgeId}`,
        { err },
      );
    }
  }

  private async updateConnectivityStatusToDB(
    edgeId: string,
    connectivityStatus: boolean,
  ) {
    try {
      logger.info(
        `Updating the Edge Connectivity Status for ${edgeId}`,
        `${JSON.stringify(connectivityStatus)}`,
      );
      const updateEdgeJobStatus = await EdgeModel.updateOne(
        { edgeId },
        {
          $set: {
            isOnline: connectivityStatus,
          },
        },
      );

      if (!updateEdgeJobStatus) {
        logger.error(`No edge found with edgeId: ${edgeId}`);
      }
      logger.info(`Edge Connectivity Status updated for ${edgeId}`);
    } catch (err) {
      logger.error(
        `Error while Updating the Edge Connectivity Status for ${edgeId}`,
        { err },
      );
    }
  }

  private async updateConnectivityAndTargetsToDB(
    edgeId: string,
    connectivityStatus: boolean,
    targetDetails: Targets[] | undefined,
  ) {
    try {
      logger.info(
        `Updating the Edge Connectivity Status and Targets for ${edgeId}`,
        `${JSON.stringify(targetDetails)}`,
      );
      const updateEdgeJobStatus = await EdgeModel.updateOne(
        { edgeId },
        {
          $set: {
            isOnline: connectivityStatus,
            targets: targetDetails,
          },
          updatedAt: new Date(),
        },
      );

      if (!updateEdgeJobStatus) {
        logger.error(`No edge found with edgeId: ${edgeId}`);
      }
      logger.info(`Edge Connectivity Status and Targets updated for ${edgeId}`);
    } catch (err) {
      logger.error(
        `Error while Updating the Edge Connectivity Status and Targets for ${edgeId}`,
        { err },
      );
    }
  }
}
