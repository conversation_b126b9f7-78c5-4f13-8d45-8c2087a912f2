service: localstack

useDotenv: false

plugins:
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 4000
    noTimeout: true
    printOutput: true
    # host: '0.0.0.0'

provider:
  name: aws
  runtime: nodejs20.x
  versionFunctions: false
  apiGateway:
    shouldStartNameWithService: true
  environment:
    STAGE: "local"

functions:
  graphql:
    handler: dist/index.default
    events:
      - httpApi:
          path: /local/graphql
          method: any
