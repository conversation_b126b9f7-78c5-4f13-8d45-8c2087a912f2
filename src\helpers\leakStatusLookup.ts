import { AlarmPayload, BrickClassWithAlarmBoundaries } from "models/AlarmPayload";
import { getBrickClassesWithThresholdValues } from "./getBrickClassesWithThresholdValues";

export const lookupForLeakStatusAndBrickClass = (payload : AlarmPayload[]) => {
  const thresholdBoundaries = getBrickClassesWithThresholdValues();
  const judgementFlags : BrickClassWithAlarmBoundaries[] = thresholdBoundaries.filter((e) => {
    return e.brickClass.includes("Refrigerant_Leak_Status");
  });

  const heatAndCoolingModeBrickClasses = thresholdBoundaries.filter((e) => {
    return !e.brickClass.includes("Refrigerant_Leak_Status");
  });

  const refleakJudgementBrickClasses = payload.filter((item: AlarmPayload) => {
    const refleakStatus = judgementFlags.map((e) => e.brickClass);
    if (refleakStatus.includes(item.brickClass)) {
      return item;
    }
    return;
  });

  if (refleakJudgementBrickClasses.length === 0) {
    return []; // No valid leak status found, return empty payload
  }

  const heatAndCoolingDeviationBrickClasses = payload.map((item:AlarmPayload)=>{
    const heatAndCoolingModeBrickList = heatAndCoolingModeBrickClasses.map(e=>e.brickClass);
    if(heatAndCoolingModeBrickList.includes(item.brickClass)){
        return {
            ...item,
            refleakStatus : Number(refleakJudgementBrickClasses.find(alarmObj => alarmObj.brickClass.includes(item.brickClass.split('_')[0]))?.measureValue)
        }
    }
    else
    return;
  }).filter(item => !!item);

  if(!heatAndCoolingDeviationBrickClasses.length){
    return []

  }
  return heatAndCoolingDeviationBrickClasses;
};