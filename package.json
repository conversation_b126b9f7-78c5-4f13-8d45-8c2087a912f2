{"name": "@ahp/ahp-pipeline-virtual-alarm-consumer", "version": "0.0.0", "description": "Example project", "license": "Carrier", "author": "Carrier Digital", "main": "dist/index.js", "types": "./dist/index.d.ts", "type": "commonjs", "files": ["/dist"], "scripts": {"build": "tsc -p ./tsconfig.build.json", "prebuild": "yarn fetch-env-vars", "check-types": "tsc --noEmit", "lint:infra": "eslint ./infra --ext .ts,.js", "lint:code": "eslint ./src --ext .ts,.js", "prepare": "husky install", "prepublish": "yarn build", "rc": "./cicd/scripts/rc.sh", "test:integration": "yarn build && jest  --runInBand --coverage --collectCoverageFrom='./src/**' --config ./jest.config.integration.js --passWithNoTests", "test:unit": "yarn build && jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.unit.js --passWithNoTests", "test": "jest --coverage", "watch": "tsc -w", "start": "tsc-watch -p ./tsconfig.build.json --onSuccess \"sls offline --stage local\"", "infra": "run-s infra:*", "infra:build": "cdk synth --context stage=dev --all", "infra:deploy": "cdk deploy --context stage=dev dev/*", "fetch-env-vars": "aws secretsmanager get-secret-value --secret-id backend-virtual-alarm-consumer-dot-env-file --query SecretString --output text --region us-east-1 > .env"}, "lint-staged": {"**/*.{json,yml,yaml}": ["prettier --write"], "**/*.{js,ts}": ["eslint --ext .js,.ts --cache --fix"]}, "dependencies": {"lodash": "^4.17.21"}, "devDependencies": {"@aws-sdk/client-ssm": "^3.468.0", "@aws-sdk/client-secrets-manager": "^3.658.1", "@carrier-io/backend-lib-logger": "^1.6.0", "@tsconfig/node20": "^20.1.4", "@types/jest": "^27.0.2", "@types/lodash": "^4.17.12", "@types/node": "^22.10.7", "aws-sdk-client-mock": "^4.1.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "@zephyr/backend-lib-infrastructure": "^1.2.6", "@carrier-io/backend-lib-axios-iam": "^2.4.0", "@carrier-io/backend-lib-pipeline-sdk": "^1.19.1", "@carrier-io/pe-lib-otel-logs": "^1.16.2", "aws-cdk": "^2.167.1", "aws-cdk-lib": "^2.176.0", "constructs": "^10.4.2", "dotenv": "^16.4.5", "esbuild": "^0.15.12", "eslint": "^8.11.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^26.1.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-sonarjs": "^0.12.0", "husky": "^8.0.0", "inquirer": "^8.2.0", "jest": "^27.2.1", "jest-sonar-reporter": "^2.0.0", "lint-staged": "13.1.0", "prettier": "^2.6.0", "source-map-support": "^0.5.20", "ts-jest": "^27.0.5", "ts-jest-resolver": "^2.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.3", "zod": "^3.24.1", "type-graphql": "^2.0.0-rc.2", "serverless": "^3.39.0", "serverless-offline": "^13.8.2", "mongoose": "^8.10.0"}}