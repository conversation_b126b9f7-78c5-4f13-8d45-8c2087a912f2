import { CioTag } from "./CioTag";

export type AlarmPayload = {
    topic : string,
    arrivalTime : number,
    assetCategory : string,
    assetSubCategory : string,
    measureValue : number,
    brickClass : string,
    alarmCode : string,
    alertCode : string,
    alarmThreshold : number,
    alertThreshold : number,
    cioTags : CioTag,
    equipmentFamily : string,
    assetId : string
    refleakStatus? : string
  };
  
  export type NodeAlarmPayload = {
    topic : string,
    payloadTopicMsg: string,
    arrivalTime : number,
    alarmCode : string,   
    alarmThreshold : number,
    clientId?: string
  };

  export type BrickClassWithAlarmBoundaries = {
    brickClass : String,
    alarmCode  : String,
    alertCode : String,
    alarmThreshold : Number | Boolean
    alertThreshold : Number | Boolean
}