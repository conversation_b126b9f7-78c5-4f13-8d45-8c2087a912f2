{"extends": "@tsconfig/node20/tsconfig.json", "compilerOptions": {"target": "ES2020", "resolveJsonModule": true, "lib": ["ES2020"], "moduleResolution": "node", "module": "CommonJS", "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "types": ["node", "jest"], "outDir": "dist", "baseUrl": "src"}, "include": ["src/**/*.ts", "src/**/*.js", "infra/**/*.ts"], "exclude": ["cdk.out"]}