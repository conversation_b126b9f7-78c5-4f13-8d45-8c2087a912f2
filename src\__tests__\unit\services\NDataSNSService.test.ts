import { mockClient } from 'aws-sdk-client-mock';
import { parseSQSEvent, communicationAlarmConsumerLambda } from '../../../lambdas/CommunicationAlarmConsumerLambda';
import { getBrickClassesWithThresholdValues } from '../../../helpers/getBrickClassesWithThresholdValues';
import eventData from '../mock/event2.json';
import { AlarmGeneratorService } from '../../../services/AlarmGeneratorService';
import { PipelinePublisherService } from '@carrier-io/backend-lib-pipeline-sdk';


beforeAll(() => {
  process.env.BRICKCLASSES_LIST = JSON.stringify([
    {
      brickClass: "Cool_Valve_Position_Deviation_Status",
      alarmThreshold: 25,
      alertThreshold: 15
    },
    {
      brickClass: "Heat_Valve_Position_Deviation_Status",
      alarmThreshold: 35,
      alertThreshold: 30
    },
    {
      brickClass: "Refrigerant_Leak_Status",
      alarmThreshold: 1,
      alertThreshold: 1,
    }
  ])
  process.env.TARGET_STREAM_NAME_DATA = `carrier-io-main-data-pipeline-dev`;
  process.env.TARGET_STREAM_NAME_LIFECYCLE = `carrier-io-main-data-pipeline-dev`;
});

jest.mock('@carrier-io/backend-lib-pipeline-sdk');


describe('publish alarms', () => {
   it('should handle error for ParseSQSEvent', async () => {
      const errorEventData = {
        Records: [
          {
            body: null,
          },
        ],
      };
      const parsedEvent = parseSQSEvent(errorEventData as any);
      expect(parsedEvent).toEqual([]);
    });
  it('should publish alarm without error', async () => {
    const mockPublish = jest.fn().mockResolvedValue([{
      error: {},
      result: true,
      timestamp: 1752813466924,
    }]);

    // Use mockReturnValue if your code calls it synchronously
    (PipelinePublisherService.newWithEnvVar as jest.Mock).mockReturnValue({
      publish: mockPublish,
    });

    process.env.TARGET_STREAM_NAME_DATA = 'sample-data-pipeline-dev';

    await communicationAlarmConsumerLambda(eventData as any);

    expect(mockPublish).toHaveBeenCalled();
  });

  it(`Reset alarm when we receive NBirth Message`, () => {
    const payload = {
      payloadTopicMsg: "NBIRTH",
      topic: "spBv1.0/ahp/NBIRTH/12345/TEST422CKZGA4723W66617",
      arrivalTime: 1741110164000,
      alarmCode: "TG01",
      alarmThreshold: 1
    };
    const sparkplugMessage: any = AlarmGeneratorService.generateNodeAlarmMessage(payload as any);

    expect(sparkplugMessage.payload.metrics.some((metric: any) => metric.name === "TG01" && !metric.value)).toBe(true);

  })
  it(`Raise alarm when we receive Ndeath Message`, () => {
    const payload = {
      payloadTopicMsg: 'NDEATH',
      topic: 'spBv1.0/ahp/NDEATH/12345/TEST422CKZGA4723W66617',
      arrivalTime: 1741110164000,
      alarmCode: 'TG01',
      alarmThreshold: 1,
    };
    const sparkplugMessage: any =
      AlarmGeneratorService.generateNodeAlarmMessage(payload as any);

    expect(
      sparkplugMessage.payload.metrics.some(
        (metric: any) => metric.name === 'TG01' && metric.value,
      ),
    ).toBe(true);
  });

  it('should throw if pipelineClient.publish fails', async () => {
    
    const payload = {
      payloadTopicMsg: 'NBIRTH',
      topic: 'spBv1.0/ahp/NBIRTH/12345/TEST422CKZGA4723W66617',
      arrivalTime: 1741110164000,
      alarmCode: 'TG01',
      alarmThreshold: 1,
    };

    const mockPublish = jest
      .fn()
      .mockRejectedValue(new Error('Publish failed'));

    (PipelinePublisherService.newWithEnvVar as jest.Mock).mockReturnValue({
      publish: mockPublish,
    });

    const service = new AlarmGeneratorService();

    jest
      .spyOn(AlarmGeneratorService, 'generateNodeAlarmMessage')
      .mockReturnValue(payload as any);

    await expect(
      service.publishNodeMessagesAlarm([payload] as any),
    ).rejects.toThrow('Publish failed');
  });

it('should throw if generateNodeAlarmMessage fails', async () => {
  const payload = {
    payloadTopicMsg: 'NBIRTH',
    topic: 'spBv1.0/ahp/NBIRTH/12345/TEST422CKZGA4723W66617',
    arrivalTime: 1741110164000,
    alarmCode: 'TG01',
    alarmThreshold: 1,
  };

  const mockPublish = jest.fn().mockRejectedValue(new Error('Publish failed'));

  (PipelinePublisherService.newWithEnvVar as jest.Mock).mockReturnValue({
    publish: mockPublish,
  });

  const service = new AlarmGeneratorService();

  jest
    .spyOn(AlarmGeneratorService, 'generateNodeAlarmMessage')
    .mockReturnValue(new Error('Generation Failed') as any);

  await expect(
    service.publishNodeMessagesAlarm([payload] as any),
  ).rejects.toThrow('Publish failed');
});
});

